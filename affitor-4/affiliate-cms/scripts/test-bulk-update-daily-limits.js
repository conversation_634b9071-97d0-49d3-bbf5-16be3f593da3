/**
 * Test script for the bulk update daily limits API
 * This script demonstrates how to use the new API endpoint
 */

const axios = require('axios');

// Configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:1337';
const NEW_DAILY_LIMIT = process.env.NEW_DAILY_LIMIT || 15;

async function testBulkUpdateDailyLimits() {
  try {
    console.log('🚀 Testing bulk update daily limits API...');
    console.log(`📍 API URL: ${API_BASE_URL}/api/user-tracking-request/update-basic-daily-limits`);
    console.log(`📊 New daily limit: ${NEW_DAILY_LIMIT}`);
    console.log('');

    const response = await axios.post(
      `${API_BASE_URL}/api/user-tracking-request/update-basic-daily-limits`,
      {
        newDailyLimit: parseInt(NEW_DAILY_LIMIT)
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('✅ API Response:');
    console.log('Status:', response.status);
    console.log('Success:', response.data.success);
    console.log('Message:', response.data.message);
    console.log('');

    if (response.data.data) {
      const { updatedCount, newDailyLimit, updatedUsers } = response.data.data;
      
      console.log('📈 Update Summary:');
      console.log(`- Updated users: ${updatedCount}`);
      console.log(`- New daily limit: ${newDailyLimit}`);
      console.log('');

      if (updatedUsers && updatedUsers.length > 0) {
        console.log('👥 Updated Users (first 5):');
        updatedUsers.slice(0, 5).forEach((user, index) => {
          console.log(`${index + 1}. ${user.username} (${user.tierName}): ${user.previousLimit} → ${user.newLimit}`);
        });
        
        if (updatedUsers.length > 5) {
          console.log(`... and ${updatedUsers.length - 5} more users`);
        }
      }
    }

    console.log('');
    console.log('🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:');
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else if (error.request) {
      console.error('No response received:', error.message);
    } else {
      console.error('Error:', error.message);
    }
    
    process.exit(1);
  }
}

// Test with invalid input
async function testInvalidInput() {
  try {
    console.log('🧪 Testing invalid input...');
    
    await axios.post(
      `${API_BASE_URL}/api/user-tracking-request/update-basic-daily-limits`,
      {
        newDailyLimit: -1 // Invalid: negative number
      }
    );
    
    console.log('❌ Should have failed with invalid input');
  } catch (error) {
    if (error.response && error.response.status === 400) {
      console.log('✅ Correctly rejected invalid input');
      console.log('Error message:', error.response.data.error?.message || error.response.data.message);
    } else {
      console.log('❌ Unexpected error:', error.message);
    }
  }
}

// Main execution
async function main() {
  console.log('🔧 Bulk Update Daily Limits API Test');
  console.log('=====================================');
  console.log('');

  // Test valid input
  await testBulkUpdateDailyLimits();
  
  console.log('');
  console.log('=====================================');
  console.log('');
  
  // Test invalid input
  await testInvalidInput();
  
  console.log('');
  console.log('🏁 All tests completed!');
}

// Run the tests
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  testBulkUpdateDailyLimits,
  testInvalidInput
};
