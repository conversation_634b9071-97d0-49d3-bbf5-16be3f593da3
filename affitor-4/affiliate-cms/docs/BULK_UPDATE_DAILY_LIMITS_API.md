# Bulk Update Daily Limits API

## Overview
This API endpoint allows updating the daily request limits for all basic tier users in bulk. This is useful for administrative purposes when you need to adjust daily limits across all basic tier subscriptions.

## Endpoint Details

### POST `/api/user-tracking-request/update-basic-daily-limits`

**Authentication:** None required (public endpoint)

**Request Body:**
```json
{
  "newDailyLimit": 15
}
```

**Parameters:**
- `newDailyLimit` (number, required): The new daily request limit to set for all basic tier users. Must be >= 0.

**Response:**
```json
{
  "success": true,
  "message": "Updated daily limits for 25 basic tier users",
  "data": {
    "updatedCount": 25,
    "newDailyLimit": 15,
    "updatedUsers": [
      {
        "trackingId": 123,
        "userId": 456,
        "username": "john_doe",
        "tierName": "basic-month",
        "previousLimit": 10,
        "newLimit": 15
      },
      // ... more users
    ]
  }
}
```

## Usage Examples

### Using cURL
```bash
curl -X POST http://localhost:1337/api/user-tracking-request/update-basic-daily-limits \
  -H "Content-Type: application/json" \
  -d '{"newDailyLimit": 15}'
```

### Using JavaScript/Fetch
```javascript
const response = await fetch('/api/user-tracking-request/update-basic-daily-limits', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    newDailyLimit: 15
  })
});

const result = await response.json();
console.log(result);
```

## How It Works

1. **Find Basic Tiers**: The API first queries all subscription tiers with names containing "basic" (case-insensitive)
2. **Find Basic Users**: It then finds all user tracking requests associated with these basic tier subscriptions
3. **Bulk Update**: Updates the `daily_request_limit` field for each basic tier user
4. **Return Results**: Provides detailed information about the update operation

## Basic Tier Detection

The API identifies basic tier users by:
- Finding subscription tiers where `name` contains "basic" (case-insensitive)
- This includes tiers like: "basic-month", "basic-quarter", "basic-year", "Basic Plan", etc.

## Error Handling

**Invalid Input:**
```json
{
  "error": {
    "message": "Valid newDailyLimit (number >= 0) is required"
  }
}
```

**No Basic Tiers Found:**
```json
{
  "success": true,
  "message": "Updated daily limits for 0 basic tier users",
  "data": {
    "updatedCount": 0,
    "newDailyLimit": 15,
    "updatedUsers": []
  }
}
```

## Security Considerations

- **No Authentication Required**: This endpoint is intentionally public for administrative ease
- **Input Validation**: Only accepts valid numeric values >= 0
- **Logging**: All operations are logged for audit purposes
- **Error Handling**: Individual user update failures don't stop the entire operation

## Use Cases

1. **Promotional Campaigns**: Temporarily increase daily limits for all basic users
2. **System Adjustments**: Adjust limits based on server capacity or business needs
3. **Emergency Changes**: Quickly modify limits during high-traffic periods
4. **A/B Testing**: Test different daily limit values across user base

## Related Endpoints

- `GET /api/user-tracking-request/my-stats` - Get individual user stats
- `POST /api/user-tracking-request/manual-daily-reset` - Reset daily count for specific user
