/**
 * user-tracking-request router
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreRouter('api::user-tracking-request.user-tracking-request', {
  config: {
    find: {
      auth: false,
    },
    findOne: {
      auth: false,
    },
  },
});

// Custom routes
export const customRoutes = {
  routes: [
    {
      method: 'GET',
      path: '/user-tracking-request/my-stats',
      handler: 'user-tracking-request.getUserStats',
      config: {
        auth: {
          scope: ['authenticated'],
        },
      },
    },
    {
      method: 'POST',
      path: '/user-tracking-request/manual-daily-reset',
      handler: 'user-tracking-request.manualDailyReset',
      config: {
        auth: {
          scope: ['authenticated'],
        },
      },
    },
    {
      method: 'POST',
      path: '/user-tracking-request/update-basic-daily-limits',
      handler: 'user-tracking-request.updateBasicDailyLimits',
      config: {
        auth: false, // No authentication required
      },
    },
  ],
};
